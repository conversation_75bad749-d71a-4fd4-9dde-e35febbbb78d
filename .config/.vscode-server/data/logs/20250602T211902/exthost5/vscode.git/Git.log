2025-06-03 05:01:32.355 [info] [main] Log level: Info
2025-06-03 05:01:32.355 [info] [main] Validating found git in: "git"
2025-06-03 05:01:32.355 [info] [main] Using git "2.47.2" from "git"
2025-06-03 05:01:32.355 [info] [Model][doInitialScan] Initial repository scan started
2025-06-03 05:01:32.355 [info] > git rev-parse --show-toplevel [34ms]
2025-06-03 05:01:32.355 [info] > git rev-parse --git-dir --git-common-dir [3ms]
2025-06-03 05:01:32.355 [info] [Model][openRepository] Opened repository (path): /home/<USER>/workspace
2025-06-03 05:01:32.355 [info] [Model][openRepository] Opened repository (real path): /home/<USER>/workspace
2025-06-03 05:01:32.360 [info] > git fetch [310ms]
2025-06-03 05:01:32.360 [info] Missing or invalid credentials.
Skip silent fetch commands
Missing or invalid credentials.
Skip silent fetch commands
remote: Repository not found.
fatal: Authentication failed for 'https://github.com/Chewy42/ChewyAI/'
2025-06-03 05:01:32.360 [info] > git config --get commit.template [305ms]
2025-06-03 05:01:32.377 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [10ms]
2025-06-03 05:01:32.509 [info] > git config --get commit.template [138ms]
2025-06-03 05:01:32.698 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [181ms]
2025-06-03 05:01:33.208 [info] > git rev-parse --show-toplevel [108ms]
2025-06-03 05:01:33.208 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [112ms]
2025-06-03 05:01:33.230 [info] > git config --get commit.template [31ms]
2025-06-03 05:01:33.231 [info] > git rev-parse --show-toplevel [10ms]
2025-06-03 05:01:33.232 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [14ms]
2025-06-03 05:01:33.237 [info] > git config --get --local branch.main.vscode-merge-base [24ms]
2025-06-03 05:01:33.285 [info] > git config --get --local branch.main.vscode-merge-base [48ms]
2025-06-03 05:01:33.612 [info] > git rev-parse --show-toplevel [364ms]
2025-06-03 05:01:33.613 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [370ms]
2025-06-03 05:01:33.613 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/origin/main refs/remotes/origin/main [373ms]
2025-06-03 05:01:33.635 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/origin/main refs/remotes/origin/main [344ms]
2025-06-03 05:01:33.644 [info] > git rev-parse --show-toplevel [18ms]
2025-06-03 05:01:33.644 [info] > git merge-base refs/heads/main refs/remotes/origin/main [23ms]
2025-06-03 05:01:33.656 [info] > git merge-base refs/heads/main refs/remotes/origin/main [15ms]
2025-06-03 05:01:33.681 [info] > git status -z -uall [15ms]
2025-06-03 05:01:33.681 [info] > git rev-parse --show-toplevel [25ms]
2025-06-03 05:01:33.681 [info] > git diff --name-status -z --diff-filter=ADMR 435a5ff9421201dec9d4e684da4764850c24096a...refs/remotes/origin/main [32ms]
2025-06-03 05:01:33.693 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [24ms]
2025-06-03 05:01:33.705 [info] > git diff --name-status -z --diff-filter=ADMR 435a5ff9421201dec9d4e684da4764850c24096a...refs/remotes/origin/main [43ms]
2025-06-03 05:01:33.849 [info] > git rev-parse --show-toplevel [157ms]
2025-06-03 05:01:33.952 [info] > git rev-parse --show-toplevel [77ms]
2025-06-03 05:01:34.051 [info] > git rev-parse --show-toplevel [87ms]
2025-06-03 05:01:34.068 [info] > git rev-parse --show-toplevel [5ms]
2025-06-03 05:01:34.077 [info] > git rev-parse --show-toplevel [3ms]
2025-06-03 05:01:34.083 [info] > git rev-parse --show-toplevel [2ms]
2025-06-03 05:01:34.219 [info] > git rev-parse --show-toplevel [132ms]
2025-06-03 05:01:34.247 [info] > git rev-parse --show-toplevel [16ms]
2025-06-03 05:01:34.621 [info] > git rev-parse --show-toplevel [178ms]
2025-06-03 05:01:34.623 [info] [Model][doInitialScan] Initial repository scan completed - repositories (1), closed repositories (0), parent repositories (0), unsafe repositories (0)
2025-06-03 05:01:34.659 [info] > git config --get commit.template [22ms]
2025-06-03 05:01:35.080 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [8ms]
2025-06-03 05:01:35.104 [info] > git status -z -uall [12ms]
2025-06-03 05:01:35.108 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [7ms]
2025-06-03 05:01:35.848 [info] > git config --get --local branch.main.github-pr-owner-number [156ms]
2025-06-03 05:01:35.848 [warning] [Git][config] git config failed: Failed to execute git
2025-06-03 05:01:36.189 [info] > git diff --name-status -z --diff-filter=ADMR 84b9e81d6e847f2506f8a35f2c8325979eeca42c [7ms]
2025-06-03 05:01:36.189 [info] fatal: bad object 84b9e81d6e847f2506f8a35f2c8325979eeca42c
2025-06-03 05:01:36.208 [info] > git diff --name-status -z --diff-filter=ADMR @{upstream} [5ms]
2025-06-03 05:03:15.310 [info] > git config --get commit.template [5ms]
2025-06-03 05:03:15.311 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-03 05:03:15.325 [info] > git status -z -uall [6ms]
2025-06-03 05:03:15.326 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-03 05:03:36.186 [info] > git diff --name-status -z --diff-filter=ADMR 84b9e81d6e847f2506f8a35f2c8325979eeca42c [1ms]
2025-06-03 05:03:36.186 [info] fatal: bad object 84b9e81d6e847f2506f8a35f2c8325979eeca42c
2025-06-03 05:03:36.194 [info] > git diff --name-status -z --diff-filter=ADMR @{upstream} [3ms]
2025-06-03 05:04:08.636 [info] > git config --get commit.template [6ms]
2025-06-03 05:04:08.636 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-03 05:04:08.647 [info] > git status -z -uall [5ms]
2025-06-03 05:04:08.648 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-03 05:05:10.759 [info] > git config --get commit.template [4ms]
2025-06-03 05:05:10.760 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-03 05:05:10.769 [info] > git status -z -uall [5ms]
2025-06-03 05:05:10.770 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-03 05:05:35.785 [info] > git config --get commit.template [4ms]
2025-06-03 05:05:35.786 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-03 05:05:35.794 [info] > git status -z -uall [4ms]
2025-06-03 05:05:35.795 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-03 05:05:36.185 [info] > git diff --name-status -z --diff-filter=ADMR 84b9e81d6e847f2506f8a35f2c8325979eeca42c [1ms]
2025-06-03 05:05:36.185 [info] fatal: bad object 84b9e81d6e847f2506f8a35f2c8325979eeca42c
2025-06-03 05:05:36.194 [info] > git diff --name-status -z --diff-filter=ADMR @{upstream} [4ms]
